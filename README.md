# <PERSON><PERSON> — Aspiring Data Scientist (IN PROGRESS PORTFOLIO)

Data scientist passionate about leveraging meaningful data-driven insights to improve lives, especially for underserved and neglected communities. I’m driven to create accessible, impactful analytical tools that empower people and bridge information gaps.

With experience in SaaS and mission-driven projects, I enjoy transforming data from raw form to actionable insights. I focus on building scalable, user-centered data solutions using modern data science and analytics technologies.

## Featured Projects

### Advanced Data Pipeline  
[github.com/Osman-OO/Adv-Pipeline](https://github.com/Osman-OO/Adv-Pipeline)  
A comprehensive, enterprise-grade data analytics platform for end-to-end data processing, statistical analysis, machine learning, and business intelligence.  
Key features include multi-source data integration (CSV, JSON, APIs, databases, and real-time streams), advanced analytics and machine learning pipelines, interactive dashboards for real-time business intelligence, automated insights like ML-powered anomaly detection and trend forecasting, and professional reporting.

### Protest Tracker  
[github.com/Yateeka/Sotware-Engineering](https://github.com/Yateeka/Sotware-Engineering)  
### Presentation Slides  
[View Project Slides](https://docs.google.com/presentation/d/12PIfNBGT12fBg4Y3Q9XMFQZ1ztV2fDp5JNuZcsr0NPU/edit?slide=id.g37236d0b311_0_6#slide=id.g37236d0b311_0_6)
A real-time data pipeline ingesting 500+ events from APIs and web scraping, improving data quality by reducing duplicates 30%. Engineered a MongoDB geospatial database with Flask API to optimize query performance. Developed an interactive Mapbox and Next.js dashboard with advanced filters, improving search speed by 50%, and collaborated in an agile, cross-functional team to deliver the project.

## In Progress Projects

### Somalia Socioeconomic Project  
Aggregating and cleaning datasets on healthcare access, education levels, water sources, and food availability to build a unified analytical framework for regional well-being analysis. Developing statistical models and geospatial analysis pipelines in Python (Pandas, GeoPandas, Scikit-learn) to detect patterns, predict resource gaps, and support evidence-based policy recommendations.

### Somali Educational Platform  
Developing a Khan Academy–style system in the Somali language focused on accessible, culturally relevant educational content for underserved communities.

---

Feel free to explore these projects or reach out to connect!
